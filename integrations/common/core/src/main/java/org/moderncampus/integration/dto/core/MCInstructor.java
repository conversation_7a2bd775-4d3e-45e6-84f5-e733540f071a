package org.moderncampus.integration.dto.core;

import java.util.List;

import org.moderncampus.integration.dto.base.BaseDTO;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AccessLevel;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.FieldDefaults;

@Getter
@Setter
@FieldDefaults(level = AccessLevel.PRIVATE)
public class MCInstructor extends BaseDTO {

    @Override
    @Schema(description = "The globally unique identifier for an instructor to be used in all external references.")
    public String getId() {
        return super.getId();
    }

    List<MCInstructorName> names;
    List<MCInstructorEmail> emails;
    List<MCInstructorType> instructorTypes;
    List<MCInstructorDepartment> instructorDepartments;

    List<MCDepartment> departments;

    String status;


    @Getter
    @Setter
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class MCInstructorEmail extends BaseDTO {

        Boolean preferred;
        MCInstructorEmailType type;
        String address;
    }

    @Getter
    @Setter
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class MCInstructorEmailType extends BaseDTO {

        String name;

    }

    @Getter
    @Setter
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class MCInstructorName extends BaseDTO {

        @Schema(description = "Indicates the preferred name for the person. Only one name should be set to preferred for a person.")
        Boolean preferred;
        MCInstructorNameType type;
        String title;
        String firstName;
        String middleName;
        String lastName;
        String suffix;
    }

    @Getter
    @Setter
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class MCInstructorNameType extends BaseDTO {

        String name;

    }

    @Getter
    @Setter
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class MCInstructorType extends BaseDTO {

    }

    @Getter
    @Setter
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class MCInstructorDepartment extends BaseDTO {

    }

    @Getter
    @Setter
    @FieldDefaults(level = AccessLevel.PRIVATE)
    public static class MCDepartment extends BaseDTO {

        @Schema(description = "The name or title of the department.")
        String name;

        @Schema(description = "The code or abbreviation of the department.")
        String code;
    }
}
